<template>
  <div class="admin-card">
    <div class="admin-card-header">
      <div class="admin-card-title">
        <i class="fas fa-chart-line admin-card-icon"></i>
        Site Profit (15% Commission)
      </div>
      <div class="admin-card-actions">
        <div class="admin-form-field">
          <select v-model="selectedPeriod" @change="onPeriodChange" class="admin-form-select admin-form-select-sm">
            <option value="week">Last 7 days</option>
            <option value="month">Last 30 days</option>
            <option value="quarter">Last 3 months</option>
            <option value="year">Last 12 months</option>
          </select>
        </div>
      </div>
    </div>
    <div class="admin-card-content">
      <div class="admin-stats-grid">
        <div class="admin-stat-item">
          <div class="admin-stat-label">Total Profit ({{ selectedPeriod === 'week' ? 'Last 7 days' : selectedPeriod === 'month' ? 'Last 30 days' : selectedPeriod === 'quarter' ? 'Last 3 months' : 'Last 12 months' }})</div>
          <div class="admin-stat-value admin-stat-value-primary">{{ formatCurrency(currentPeriodProfit) }}</div>
        </div>
        <div class="admin-stat-item">
          <div class="admin-stat-label">Average Daily</div>
          <div class="admin-stat-value admin-stat-value-secondary">{{ formatCurrency(averageDailyProfit) }}</div>
        </div>
      </div>

      <div v-if="loading" class="admin-loading-state">
        <div class="admin-spinner">
          <i class="fas fa-spinner fa-pulse"></i>
        </div>
        <p class="admin-loading-text">Loading profit data...</p>
      </div>
      <div v-else-if="!data || data.length === 0" class="admin-empty-state">
        <div class="admin-empty-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <p class="admin-empty-text">No profit data available for this period</p>
      </div>
      <div v-else class="admin-chart-container">
        <canvas ref="chartCanvas" :id="`profit-chart-${Math.random().toString(36).substr(2, 9)}`"></canvas>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  Title,
  Tooltip,
  Legend,
  Filler
);

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  totalProfit: {
    type: Number,
    default: 0
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['period-changed']);

// Reactive data
const chartCanvas = ref(null);
const selectedPeriod = ref('month');
const loading = ref(false);
let chartInstance = null;

// Computed properties
const averageDailyProfit = computed(() => {
  if (!props.data || props.data.length === 0) return 0;
  const totalDays = props.data.length;
  const totalValue = props.data.reduce((sum, item) => sum + (item.value || 0), 0);
  return totalValue / totalDays;
});

// Computed total profit for the current period
const currentPeriodProfit = computed(() => {
  if (!props.data || props.data.length === 0) return 0;
  return props.data.reduce((sum, item) => sum + (item.value || 0), 0);
});

// Format currency
const formatCurrency = (value) => {
  const numValue = typeof value === 'string' ? Number(value) : value;
  if (isNaN(numValue)) return 'UAH 0.00';
  
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numValue);
};

// Handle period change
const onPeriodChange = () => {
  emit('period-changed', selectedPeriod.value);
};

// Initialize chart
const initChart = () => {
  if (!chartCanvas.value || !props.data || props.data.length === 0) {
    console.warn('Chart canvas or data not available for SiteProfitChart');
    return;
  }

  // Destroy existing chart
  if (chartInstance) {
    try {
      chartInstance.destroy();
    } catch (e) {
      console.warn('Error destroying chart:', e);
    }
    chartInstance = null;
  }

  const ctx = chartCanvas.value.getContext('2d');
  
  chartInstance = new ChartJS(ctx, {
    type: 'line',
    data: {
      labels: props.data.map(item => item.label || item.date),
      datasets: [{
        label: 'Site Profit (UAH)',
        data: props.data.map(item => item.value || 0),
        borderColor: '#ff7700',
        backgroundColor: 'rgba(255, 119, 0, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#ff7700',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 3,
        pointRadius: 6,
        pointHoverRadius: 10,
        pointHoverBackgroundColor: '#e66a00',
        pointHoverBorderColor: '#ffffff',
        pointHoverBorderWidth: 3
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: 'index'
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(31, 41, 55, 0.95)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: '#ff7700',
          borderWidth: 2,
          cornerRadius: 8,
          padding: 12,
          titleFont: {
            size: 14,
            weight: 'bold'
          },
          bodyFont: {
            size: 13
          },
          callbacks: {
            label: function(context) {
              return `Profit: ${formatCurrency(context.parsed.y)}`;
            }
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            color: '#6b7280',
            font: {
              size: 12,
              weight: '500'
            },
            padding: 8,
            callback: function(value) {
              return formatCurrency(value);
            }
          },
          grid: {
            color: 'rgba(107, 114, 128, 0.2)',
            drawBorder: false
          },
          border: {
            display: false
          }
        },
        x: {
          ticks: {
            color: '#6b7280',
            font: {
              size: 12,
              weight: '500'
            },
            padding: 8,
            maxRotation: 45
          },
          grid: {
            display: false
          },
          border: {
            display: false
          }
        }
      }
    }
  });
};

// Update chart when data changes
watch(() => props.data, (newData) => {
  loading.value = false;

  // Wait for next tick to ensure DOM is updated
  setTimeout(() => {
    if (newData && newData.length > 0 && chartCanvas.value) {
      initChart();
    }
  }, 100); // Increased timeout for better reliability
}, { deep: true });

onMounted(() => {
  // Wait for DOM to be fully rendered
  setTimeout(() => {
    if (props.data && props.data.length > 0 && chartCanvas.value) {
      initChart();
    }
  }, 100);
});

onUnmounted(() => {
  if (chartInstance) {
    try {
      chartInstance.destroy();
    } catch (e) {
      console.warn('Error destroying chart on unmount:', e);
    }
    chartInstance = null;
  }
});
</script>

<style scoped>
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

.card-header-icon .field {
  margin-bottom: 0;
}

.level {
  margin-bottom: 1.5rem;
}

.heading {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: #7a7a7a;
  margin-bottom: 0.25rem;
}

.title {
  margin-bottom: 0;
}
</style>
