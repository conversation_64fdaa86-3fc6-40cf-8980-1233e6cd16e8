<template>
  <div class="admin-card admin-sales-chart">
    <div class="admin-card-header">
      <h3 class="admin-card-title">
        <i class="fas fa-chart-line"></i>
        Sales Overview
      </h3>
      <div class="admin-chart-tabs">
        <div class="admin-tab-group">
          <button
            :class="['admin-tab', { 'active': selectedPeriod === 'week' }]"
            @click="changePeriod('week')">
            Week
          </button>
          <button
            :class="['admin-tab', { 'active': selectedPeriod === 'month' }]"
            @click="changePeriod('month')">
            Month
          </button>
          <button
            :class="['admin-tab', { 'active': selectedPeriod === 'year' }]"
            @click="changePeriod('year')">
            Year
          </button>
        </div>
      </div>
    </div>
    <div class="admin-card-content">
      <div v-if="loading" class="admin-loading-state">
        <div class="admin-spinner">
          <i class="fas fa-spinner fa-pulse"></i>
        </div>
        <p class="admin-loading-text">Loading chart data...</p>
      </div>
      <div v-else-if="!data || data.length === 0" class="admin-empty-state">
        <div class="admin-empty-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <p class="admin-empty-text">No sales data available for this period</p>
      </div>
      <div v-else class="admin-chart-container">
        <canvas ref="chartCanvas" :id="`sales-chart-${Math.random().toString(36).substr(2, 9)}`" height="300"></canvas>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  Title,
  Tooltip,
  Legend,
  Filler
);

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['period-changed']);

const chartCanvas = ref(null);
const chart = ref(null);
const selectedPeriod = ref('month');
const loading = ref(false);

// Change period handler
const changePeriod = (period) => {
  if (selectedPeriod.value === period) return;

  selectedPeriod.value = period;
  loading.value = true;
  emit('period-changed', period);
};

// Computed properties for chart data
const chartData = computed(() => {
  // Validate data
  if (!props.data || !Array.isArray(props.data) || props.data.length === 0) {
    return {
      labels: [],
      datasets: []
    };
  }

  return {
    labels: props.data.map(item => item.label || 'Unknown'),
    datasets: [
      {
        label: 'Sales',
        data: props.data.map(item => Number(item.value) || 0),
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        borderColor: 'var(--admin-primary)',
        borderWidth: 3,
        pointBackgroundColor: 'var(--admin-primary)',
        pointBorderColor: 'var(--admin-white)',
        pointBorderWidth: 2,
        pointRadius: 5,
        pointHoverRadius: 8,
        tension: 0.4,
        fill: true
      }
    ]
  };
});

// Initialize chart
const initChart = () => {
  // Destroy existing chart if it exists
  if (chart.value) {
    try {
      chart.value.destroy();
    } catch (e) {
      console.warn('Error destroying chart:', e);
    }
    chart.value = null;
  }

  if (!chartCanvas.value) {
    console.warn('Chart canvas not available for SalesChart');
    return;
  }

  // Validate data before creating chart
  if (!props.data || !Array.isArray(props.data) || props.data.length === 0) {
    console.warn('Invalid or empty data for SalesChart');
    return;
  }

  const ctx = chartCanvas.value.getContext('2d');
  if (!ctx) {
    console.warn('Unable to get 2D context for SalesChart');
    return;
  }

  chart.value = new ChartJS(ctx, {
    type: 'line',
    data: chartData.value,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'var(--admin-gray-900)',
          titleColor: 'var(--admin-white)',
          bodyColor: 'var(--admin-white)',
          borderColor: 'var(--admin-primary)',
          borderWidth: 1,
          titleFont: {
            size: 14,
            weight: 'bold'
          },
          bodyFont: {
            size: 13
          },
          padding: 16,
          cornerRadius: 8,
          callbacks: {
            label: function(context) {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.parsed.y !== null) {
                label += new Intl.NumberFormat('uk-UA', {
                  style: 'currency',
                  currency: 'UAH'
                }).format(context.parsed.y);
              }
              return label;
            }
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            color: 'var(--admin-gray-600)',
            font: {
              weight: '500',
              size: 12
            },
            callback: function(value) {
              return new Intl.NumberFormat('uk-UA', {
                style: 'currency',
                currency: 'UAH',
                notation: 'compact',
                compactDisplay: 'short'
              }).format(value);
            }
          },
          grid: {
            color: 'var(--admin-border-color)',
            borderDash: [2, 2]
          }
        },
        x: {
          ticks: {
            color: 'var(--admin-gray-600)',
            font: {
              weight: '500',
              size: 12
            }
          },
          grid: {
            display: false
          }
        }
      }
    }
  });
};

// Update chart when data changes
watch(() => props.data, (newData) => {
  loading.value = false;

  // Wait for next tick to ensure DOM is updated
  setTimeout(() => {
    if (newData && newData.length > 0 && chartCanvas.value) {
      initChart();
    }
  }, 100); // Increased timeout for better reliability
}, { deep: true });

onMounted(() => {
  // Wait for DOM to be fully rendered
  setTimeout(() => {
    if (props.data && props.data.length > 0 && chartCanvas.value) {
      initChart();
    }
  }, 100);
});

onUnmounted(() => {
  if (chart.value) {
    try {
      chart.value.destroy();
    } catch (e) {
      console.warn('Error destroying chart on unmount:', e);
    }
    chart.value = null;
  }
});
</script>

<style scoped>
.admin-sales-chart {
  height: 100%;
}

.admin-chart-tabs {
  display: flex;
  align-items: center;
}

.admin-tab-group {
  display: flex;
  background: var(--admin-gray-100);
  border-radius: var(--admin-radius-lg);
  padding: 4px;
  gap: 2px;
}

.admin-tab {
  background: none;
  border: none;
  padding: var(--admin-space-sm) var(--admin-space-md);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-600);
  cursor: pointer;
  transition: all var(--admin-transition-fast);
}

.admin-tab:hover {
  color: var(--admin-gray-900);
  background: var(--admin-gray-200);
}

.admin-tab.active {
  background: var(--admin-primary);
  color: white;
  box-shadow: var(--admin-shadow-sm);
}

.admin-chart-container {
  height: 300px;
  position: relative;
  padding: var(--admin-space-md);
}

.admin-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-600);
}

.admin-spinner {
  font-size: 2rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-md);
}

.admin-loading-text {
  font-size: var(--admin-text-sm);
  margin: 0;
}

.admin-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-empty-icon {
  font-size: 3rem;
  color: var(--admin-gray-400);
  margin-bottom: var(--admin-space-lg);
}

.admin-empty-text {
  font-size: var(--admin-text-base);
  margin: 0;
  text-align: center;
}
</style>
