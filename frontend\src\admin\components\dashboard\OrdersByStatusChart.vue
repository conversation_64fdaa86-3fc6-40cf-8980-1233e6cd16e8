<template>
  <div class="admin-card admin-orders-chart">
    <div class="admin-card-header">
      <h3 class="admin-card-title">
        <i class="fas fa-chart-pie"></i>
        Orders by Status
      </h3>
    </div>
    <div class="admin-card-content">
      <div v-if="loading" class="admin-loading-state">
        <div class="admin-spinner">
          <i class="fas fa-spinner fa-pulse"></i>
        </div>
        <p class="admin-loading-text">Loading chart data...</p>
      </div>
      <div v-else-if="!data || data.length === 0" class="admin-empty-state">
        <div class="admin-empty-icon">
          <i class="fas fa-chart-pie"></i>
        </div>
        <p class="admin-empty-text">No order data available</p>
      </div>
      <div v-else class="admin-chart-wrapper">
        <div class="admin-chart-container">
          <canvas ref="chartCanvas"></canvas>
        </div>
        <div class="admin-status-legend">
          <div
            v-for="(item, index) in data"
            :key="item.status"
            class="admin-legend-item">
            <span
              class="admin-legend-color"
              :style="{ backgroundColor: chartColors[index % chartColors.length] }">
            </span>
            <span class="admin-legend-label">{{ item.status }}</span>
            <span class="admin-legend-value">{{ item.count }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend
);

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const chartCanvas = ref(null);
const chart = ref(null);
const loading = ref(false);

// Chart colors - Admin theme colors (actual hex values)
const chartColors = [
  '#ff7700',                  // Primary (Orange)
  '#10b981',                  // Success (Green)
  '#f59e0b',                  // Warning (Amber)
  '#ef4444',                  // Danger (Red)
  '#3b82f6',                  // Info (Blue)
  '#8b5cf6',                  // Purple
  '#06b6d4',                  // Cyan
  '#f97316',                  // Orange variant
  '#ec4899',                  // Pink
  '#6366f1'                   // Indigo
];

// Initialize chart
const createChart = () => {
  if (!chartCanvas.value) {
    console.warn('Chart canvas not available for OrdersByStatusChart');
    return;
  }

  // Validate data before creating chart
  if (!props.data || !Array.isArray(props.data) || props.data.length === 0) {
    console.warn('Invalid or empty data for OrdersByStatusChart');
    return;
  }

  const ctx = chartCanvas.value.getContext('2d');
  if (!ctx) {
    console.warn('Unable to get 2D context for OrdersByStatusChart');
    return;
  }

  // Destroy existing chart if it exists
  if (chart.value) {
    chart.value.destroy();
    chart.value = null;
  }

  // Extract labels and data from props with validation
  const labels = props.data.map(item => item.status || 'Unknown');
  const data = props.data.map(item => Number(item.count) || 0);

  // Create chart
  chart.value = new ChartJS(ctx, {
    type: 'doughnut',
    data: {
      labels: labels,
      datasets: [{
        data: data,
        backgroundColor: chartColors.slice(0, props.data.length),
        borderColor: '#ffffff',
        borderWidth: 3,
        hoverOffset: 15,
        hoverBorderWidth: 4,
        hoverBackgroundColor: chartColors.slice(0, props.data.length).map(color => color + 'dd'),
        hoverBorderColor: '#ffffff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      cutout: '40%',
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(31, 41, 55, 0.95)',
          titleFont: {
            size: 14,
            weight: 'bold'
          },
          bodyFont: {
            size: 13,
            weight: '500'
          },
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: '#ff7700',
          borderWidth: 2,
          padding: 12,
          cornerRadius: 8,
          boxPadding: 6,
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              const value = context.raw || 0;
              const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
              const percentage = Math.round((value / total) * 100);
              return `${label}: ${value} (${percentage}%)`;
            }
          }
        }
      }
    }
  });
};

const updateChart = () => {
  if (!chart.value) return;

  // Update chart data
  chart.value.data.labels = props.data.map(item => item.status);
  chart.value.data.datasets[0].data = props.data.map(item => item.count);
  chart.value.update();
};

// Watch for data changes
watch(() => props.data, (newData) => {
  loading.value = false;

  // Wait for next tick to ensure DOM is updated
  setTimeout(() => {
    if (newData && newData.length > 0 && chartCanvas.value) {
      if (chart.value) {
        updateChart();
      } else {
        createChart();
      }
    }
  }, 100); // Increased timeout for better reliability
}, { deep: true });

onMounted(() => {
  // Wait for DOM to be fully rendered
  setTimeout(() => {
    if (props.data && props.data.length > 0 && chartCanvas.value) {
      createChart();
    }
  }, 100);
});
</script>

<style scoped>
.admin-orders-chart {
  height: 100%;
}

.admin-chart-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
}

.admin-chart-container {
  height: 250px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-md);
}

.admin-status-legend {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--admin-space-md);
  padding: var(--admin-space-md);
  background: var(--admin-gray-50);
  border-radius: var(--admin-radius-lg);
  border: 1px solid var(--admin-border-color);
}

.admin-legend-item {
  display: flex;
  align-items: center;
  padding: var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  transition: all var(--admin-transition-fast);
}

.admin-legend-item:hover {
  background: var(--admin-white);
  box-shadow: var(--admin-shadow-sm);
}

.admin-legend-color {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: var(--admin-radius-full);
  margin-right: var(--admin-space-md);
  border: 2px solid var(--admin-white);
  box-shadow: var(--admin-shadow-sm);
}

.admin-legend-label {
  flex: 1;
  font-weight: var(--admin-font-medium);
  color: var(--admin-gray-700);
  font-size: var(--admin-text-sm);
}

.admin-legend-value {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-gray-900);
  font-size: var(--admin-text-sm);
  background: var(--admin-primary-bg);
  color: var(--admin-primary-dark);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-md);
  min-width: 32px;
  text-align: center;
}

.admin-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-600);
}

.admin-spinner {
  font-size: 2rem;
  color: var(--admin-primary);
  margin-bottom: var(--admin-space-md);
}

.admin-loading-text {
  font-size: var(--admin-text-sm);
  margin: 0;
}

.admin-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-4xl);
  color: var(--admin-gray-500);
}

.admin-empty-icon {
  font-size: 3rem;
  color: var(--admin-gray-400);
  margin-bottom: var(--admin-space-lg);
}

.admin-empty-text {
  font-size: var(--admin-text-base);
  margin: 0;
  text-align: center;
}

@media (max-width: 768px) {
  .admin-status-legend {
    grid-template-columns: 1fr;
  }

  .admin-chart-container {
    height: 200px;
  }
}
</style>
