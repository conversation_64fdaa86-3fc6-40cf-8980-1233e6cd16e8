<template>
  <div class="admin-seller-request-detail">
    <div class="admin-page-header">
      <div class="admin-header-content">
        <div class="admin-header-left">
          <h1 class="admin-page-title">
            <i class="fas fa-store"></i>
            Seller Request Details
          </h1>
        </div>
        <div class="admin-header-right">
          <router-link to="/admin/seller-requests" class="admin-btn admin-btn-secondary">
            <i class="fas fa-arrow-left"></i>
            <span>Back to Seller Requests</span>
          </router-link>
        </div>
      </div>
    </div>

    <div v-if="loading" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading seller request details...</p>
    </div>
    <div v-else-if="error" class="admin-alert admin-alert-danger">
      <button class="admin-alert-close" @click="error = null">
        <i class="fas fa-times"></i>
      </button>
      {{ error }}
    </div>
    <div v-else-if="!request.id" class="admin-alert admin-alert-warning">
      <p>Seller request not found.</p>
      <router-link to="/admin/seller-requests" class="admin-btn admin-btn-primary">
        Back to Seller Requests
      </router-link>
    </div>
    <div v-else>
      <!-- Request Header -->
      <div class="admin-card">
        <div class="admin-card-content">
          <div class="admin-seller-request-header">
            <div class="admin-seller-request-info">
              <h2 class="admin-seller-request-title">{{ request.companyRequestData?.name || 'Seller Request' }}</h2>
              <div class="admin-seller-request-meta">
                <status-badge
                  :status="getStatusString(request.status)"
                  type="default" />
                <span class="admin-seller-request-date">Submitted on {{ formatDate(request.createdAt) }}</span>
              </div>
            </div>
            <div class="admin-seller-request-actions">
              <div v-if="getStatusString(request.status) === 'pending'" class="admin-seller-request-status-actions">
                <button
                  class="admin-btn admin-btn-success"
                  @click="confirmApprove"
                  :disabled="processing">
                  <i class="fas fa-check"></i>
                  <span>Approve</span>
                </button>
                <button
                  class="admin-btn admin-btn-danger"
                  @click="confirmReject"
                  :disabled="processing">
                  <i class="fas fa-times"></i>
                  <span>Reject</span>
                </button>
              </div>
              <div v-else-if="getStatusString(request.status) === 'approved'" class="admin-alert admin-alert-success">
                <i class="fas fa-check-circle"></i>
                <span>Approved on {{ formatDate(request.updatedAt) }}</span>
              </div>
              <div v-else-if="getStatusString(request.status) === 'rejected'" class="admin-alert admin-alert-danger">
                <i class="fas fa-times-circle"></i>
                <span>Rejected on {{ formatDate(request.updatedAt) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="admin-grid admin-grid-seller-request">
        <!-- User Information -->
        <div class="admin-card">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-user admin-card-icon"></i>
              Applicant Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-user-avatar-section">
              <div class="admin-user-avatar admin-user-avatar-large">
                {{ getUserInitials(request.user?.username) }}
              </div>
            </div>

            <div class="admin-info-group">
              <label class="admin-info-label">Username</label>
              <div class="admin-info-value">{{ request.user?.username || 'N/A' }}</div>
            </div>

            <div class="admin-info-group">
              <label class="admin-info-label">Email</label>
              <div class="admin-info-value">
                <a v-if="request.user?.email" :href="`mailto:${request.user.email}`" class="admin-link">
                  {{ request.user.email }}
                </a>
                <span v-else>N/A</span>
              </div>
            </div>

            <div class="admin-info-group">
              <label class="admin-info-label">Role</label>
              <div class="admin-info-value">
                <span class="admin-badge admin-badge-secondary">{{ request.user?.role || 'Not provided' }}</span>
              </div>
            </div>

            <div class="admin-info-group">
              <label class="admin-info-label">Registered Since</label>
              <div class="admin-info-value">{{ formatDate(request.user?.createdAt) }}</div>
            </div>

            <div class="admin-info-group">
              <label class="admin-info-label">User Profile</label>
              <div class="admin-info-value">
                <router-link
                  v-if="request.userId"
                  :to="`/admin/users/${request.userId}`"
                  class="admin-btn admin-btn-sm admin-btn-primary">
                  <i class="fas fa-user"></i>
                  View Profile
                </router-link>
                <span v-else>N/A</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Company Information -->
        <div class="admin-card admin-card-company">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-building admin-card-icon"></i>
              Company Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid admin-form-grid-2">
              <div class="admin-info-group">
                <label class="admin-info-label">Company Name</label>
                <div class="admin-info-value">{{ request.companyRequestData?.name || 'N/A' }}</div>
              </div>

              <div class="admin-info-group">
                <label class="admin-info-label">Contact Email</label>
                <div class="admin-info-value">
                  <a v-if="request.companyRequestData?.contactEmail"
                     :href="`mailto:${request.companyRequestData.contactEmail}`"
                     class="admin-link">
                    {{ request.companyRequestData.contactEmail }}
                  </a>
                  <span v-else>N/A</span>
                </div>
              </div>

              <div class="admin-info-group">
                <label class="admin-info-label">Contact Phone</label>
                <div class="admin-info-value">
                  <a v-if="request.companyRequestData?.contactPhone"
                     :href="`tel:${request.companyRequestData.contactPhone}`"
                     class="admin-link">
                    {{ request.companyRequestData.contactPhone }}
                  </a>
                  <span v-else>N/A</span>
                </div>
              </div>

              <div class="admin-info-group">
                <label class="admin-info-label">Address</label>
                <div class="admin-info-value">{{ getFullAddress() }}</div>
              </div>

              <div class="admin-info-group admin-info-group-full">
                <label class="admin-info-label">Description</label>
                <div class="admin-info-value admin-info-value-multiline">
                  {{ request.companyRequestData?.description || 'N/A' }}
                </div>
              </div>

              <div class="admin-info-group">
                <label class="admin-info-label">Meta Title</label>
                <div class="admin-info-value">{{ request.companyRequestData?.metaTitle || 'N/A' }}</div>
              </div>

              <div class="admin-info-group">
                <label class="admin-info-label">Meta Description</label>
                <div class="admin-info-value">{{ request.companyRequestData?.metaDescription || 'N/A' }}</div>
              </div>
            </div>

            <div v-if="request.companyRequestData?.imageUrl" class="admin-info-group admin-mt-4">
              <label class="admin-info-label">Company Image</label>
              <div class="admin-info-value">
                <div class="admin-image-preview">
                  <img :src="request.companyRequestData.imageUrl" alt="Company image" class="admin-image-preview-img">
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Finance Information -->
        <div class="admin-card">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-credit-card admin-card-icon"></i>
              Finance Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid admin-form-grid-2">
              <div class="admin-info-group">
                <label class="admin-info-label">Bank Name</label>
                <div class="admin-info-value">{{ request.financeRequestData?.bankName || 'N/A' }}</div>
              </div>

              <div class="admin-info-group">
                <label class="admin-info-label">Bank Account</label>
                <div class="admin-info-value">{{ request.financeRequestData?.bankAccount || 'N/A' }}</div>
              </div>

              <div class="admin-info-group">
                <label class="admin-info-label">Bank Code</label>
                <div class="admin-info-value">{{ request.financeRequestData?.bankCode || 'N/A' }}</div>
              </div>

              <div class="admin-info-group">
                <label class="admin-info-label">Tax ID</label>
                <div class="admin-info-value">{{ request.financeRequestData?.taxId || 'N/A' }}</div>
              </div>

              <div class="admin-info-group admin-info-group-full">
                <label class="admin-info-label">Payment Details</label>
                <div class="admin-info-value admin-info-value-multiline">{{ request.financeRequestData?.paymentDetails || 'N/A' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Schedule Information -->
        <div class="admin-card">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-clock admin-card-icon"></i>
              Schedule Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div v-if="request.scheduleRequestData?.daySchedules && request.scheduleRequestData.daySchedules.length > 0" class="admin-schedule-table">
              <div class="admin-schedule-header">
                <div class="admin-schedule-header-cell">Day</div>
                <div class="admin-schedule-header-cell">Open Time</div>
                <div class="admin-schedule-header-cell">Close Time</div>
                <div class="admin-schedule-header-cell">Status</div>
              </div>
              <div class="admin-schedule-body">
                <div v-for="schedule in request.scheduleRequestData.daySchedules" :key="schedule.day" class="admin-schedule-row">
                  <div class="admin-schedule-cell admin-schedule-day">
                    <strong>{{ getDayName(schedule.day) }}</strong>
                  </div>
                  <div class="admin-schedule-cell">
                    {{ schedule.isClosed ? '-' : (schedule.openTime || 'Not set') }}
                  </div>
                  <div class="admin-schedule-cell">
                    {{ schedule.isClosed ? '-' : (schedule.closeTime || 'Not set') }}
                  </div>
                  <div class="admin-schedule-cell">
                    <span class="admin-badge" :class="schedule.isClosed ? 'admin-badge-danger' : 'admin-badge-success'">
                      {{ schedule.isClosed ? 'Closed' : 'Open' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="admin-empty-state">
              <i class="fas fa-clock admin-empty-icon"></i>
              <p class="admin-empty-text">No schedule information provided</p>
            </div>
          </div>
        </div>

        <!-- Additional Information -->
        <div class="admin-card">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-info-circle admin-card-icon"></i>
              Additional Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div v-if="request.additionalInformation" class="admin-info-group">
              <div class="admin-info-value admin-info-value-multiline">{{ request.additionalInformation }}</div>
            </div>
            <div v-else class="admin-empty-state">
              <i class="fas fa-info-circle admin-empty-icon"></i>
              <p class="admin-empty-text">No additional information provided</p>
            </div>
          </div>
        </div>

        <!-- Documents -->
        <div v-if="request.documents && request.documents.length > 0" class="admin-card">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-file-alt admin-card-icon"></i>
              Documents
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-documents-list">
              <div
                v-for="(document, index) in request.documents"
                :key="index"
                class="admin-document-item">
                <div class="admin-document-icon">
                  <i :class="getDocumentIcon(document.type)"></i>
                </div>
                <div class="admin-document-info">
                  <h4 class="admin-document-title">{{ document.name }}</h4>
                  <p class="admin-document-type">{{ document.type }}</p>
                </div>
                <div class="admin-document-actions">
                  <a
                    :href="document.url"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="admin-btn admin-btn-sm admin-btn-primary">
                    <i class="fas fa-download"></i>
                    Download
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Rejection Reason -->
        <div v-if="request.status === 'rejected' && request.rejectionReason" class="admin-card admin-card-danger">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-times-circle admin-card-icon"></i>
              Rejection Reason
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-info-value admin-info-value-multiline">{{ request.rejectionReason }}</div>
          </div>
        </div>
        </div>
      </div>
    </div>

    <!-- Approve Confirmation Modal -->
    <confirm-dialog
      :is-open="showApproveModal"
      title="Approve Seller Request"
      :message="`Are you sure you want to approve ${request.user?.firstName || 'this user'} ${request.user?.lastName || ''}'s seller request for '${request.storeName || 'this store'}'?`"
      confirm-text="Approve"
      cancel-text="Cancel"
      confirm-button-class="is-success"
      @confirm="approveRequest"
      @cancel="cancelProcess" />

    <!-- Reject Modal -->
    <div class="modal" :class="{ 'is-active': showRejectModal }">
      <div class="modal-background" @click="cancelProcess"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Reject Seller Request</p>
          <button class="delete" aria-label="close" @click="cancelProcess"></button>
        </header>
        <section class="modal-card-body">
          <p>Are you sure you want to reject {{ request.user?.firstName || 'this user' }}'s seller request for '{{ request.storeName || 'this store' }}'?</p>

          <div class="admin-form-group admin-mt-4">
            <label class="admin-form-label">Reason for Rejection (Optional)</label>
            <textarea
              class="admin-form-textarea"
              v-model="rejectionReason"
              placeholder="Provide a reason for rejection"
              rows="3">
            </textarea>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="admin-btn admin-btn-danger"
            @click="rejectRequest"
            :disabled="processing">
            <i v-if="processing" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-times"></i>
            {{ processing ? 'Rejecting...' : 'Reject' }}
          </button>
          <button class="admin-btn admin-btn-secondary" @click="cancelProcess">
            <i class="fas fa-times"></i>
            Cancel
          </button>
        </footer>
      </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { sellerRequestsService } from '@/admin/services/seller-requests';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(true);
const error = ref(null);
const request = ref({});
const processing = ref(false);

// Modal state
const showApproveModal = ref(false);
const showRejectModal = ref(false);
const rejectionReason = ref('');

// Computed properties
const requestId = computed(() => route.params.id);

// Fetch request data
const fetchRequest = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await sellerRequestsService.getSellerRequest(requestId.value);
    request.value = response.data;
  } catch (err) {
    console.error('Error fetching seller request:', err);
    error.value = 'Failed to load seller request data. Please try again.';
  } finally {
    loading.value = false;
  }
};

// Get full address
const getFullAddress = () => {
  const data = request.value?.companyRequestData;
  if (!data) return 'N/A';

  const parts = [
    data.addressStreet,
    data.addressCity,
    data.addressRegion,
    data.addressPostalCode
  ].filter(Boolean);

  return parts.length > 0 ? parts.join(', ') : 'N/A';
};

// Get day name
const getDayName = (dayNumber) => {
  // Handle different day numbering systems
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  // Convert to number if it's a string
  const dayIndex = typeof dayNumber === 'string' ? parseInt(dayNumber, 10) : dayNumber;

  // Handle 1-based indexing (Monday = 1) vs 0-based indexing (Sunday = 0)
  if (dayIndex >= 1 && dayIndex <= 7) {
    // If it's 1-7, assume Monday=1, Sunday=7 format
    const adjustedIndex = dayIndex === 7 ? 0 : dayIndex;
    return days[adjustedIndex] || 'Unknown';
  } else if (dayIndex >= 0 && dayIndex <= 6) {
    // If it's 0-6, assume Sunday=0 format
    return days[dayIndex] || 'Unknown';
  }

  return 'Unknown';
};

// Convert status enum to string
const getStatusString = (status) => {
  if (typeof status === 'string') {
    return status.toLowerCase();
  }

  switch (status) {
    case 0: return 'pending';
    case 1: return 'approved';
    case 2: return 'rejected';
    default: return 'pending';
  }
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(dateString));
};

// Handle image error
const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/150?text=No+Image';
};

// Get social class
const getSocialClass = (platform) => {
  switch (platform.toLowerCase()) {
    case 'facebook':
      return 'link';
    case 'twitter':
      return 'info';
    case 'instagram':
      return 'danger';
    case 'linkedin':
      return 'link';
    case 'youtube':
      return 'danger';
    default:
      return 'light';
  }
};

// Get document icon
const getDocumentIcon = (type) => {
  if (!type) return 'fas fa-file';

  type = type.toLowerCase();

  if (type.includes('pdf')) {
    return 'fas fa-file-pdf';
  } else if (type.includes('word') || type.includes('doc')) {
    return 'fas fa-file-word';
  } else if (type.includes('excel') || type.includes('xls')) {
    return 'fas fa-file-excel';
  } else if (type.includes('image') || type.includes('jpg') || type.includes('png')) {
    return 'fas fa-file-image';
  } else {
    return 'fas fa-file';
  }
};

// Confirm approve
const confirmApprove = () => {
  showApproveModal.value = true;
};

// Confirm reject
const confirmReject = () => {
  rejectionReason.value = '';
  showRejectModal.value = true;
};

// Cancel process
const cancelProcess = () => {
  showApproveModal.value = false;
  showRejectModal.value = false;
  rejectionReason.value = '';
};

// Approve request
const approveRequest = async () => {
  processing.value = true;

  try {
    await sellerRequestsService.approveSellerRequest(requestId.value);

    // Update request data
    request.value.status = 1; // Approved
    request.value.approvedAt = new Date();

    // Close modal
    showApproveModal.value = false;
  } catch (err) {
    console.error('Error approving seller request:', err);
    error.value = 'Failed to approve seller request. Please try again.';
  } finally {
    processing.value = false;
  }
};

// Reject request
const rejectRequest = async () => {
  processing.value = true;

  try {
    await sellerRequestsService.rejectSellerRequest(
      requestId.value,
      rejectionReason.value
    );

    // Update request data
    request.value.status = 2; // Rejected
    request.value.rejectedAt = new Date();
    request.value.rejectionReason = rejectionReason.value;

    // Close modal
    showRejectModal.value = false;
    rejectionReason.value = '';
  } catch (err) {
    console.error('Error rejecting seller request:', err);
    error.value = 'Failed to reject seller request. Please try again.';
  } finally {
    processing.value = false;
  }
};

// Utility functions
const getUserInitials = (username) => {
  if (!username) return '?';
  return username.substring(0, 2).toUpperCase();
};

// Lifecycle hooks
onMounted(() => {
  fetchRequest();
});
</script>

<style scoped>
/* Admin Grid Layout */
.admin-grid {
  display: grid;
  gap: var(--admin-spacing-4);
}

.admin-grid-seller-request {
  grid-template-columns: 1fr 2fr;
  grid-template-areas:
    "user company"
    "finance schedule"
    "additional documents"
    "rejection rejection";
}

.admin-card-company {
  grid-area: company;
}

.admin-card:nth-child(1) {
  grid-area: user;
}

.admin-card:nth-child(3) {
  grid-area: finance;
}

.admin-card:nth-child(4) {
  grid-area: schedule;
}

.admin-card:nth-child(5) {
  grid-area: additional;
}

.admin-card:nth-child(6) {
  grid-area: documents;
}

.admin-card-danger {
  grid-area: rejection;
}

/* Seller Request Header */
.admin-seller-request-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--admin-spacing-4);
}

.admin-seller-request-info {
  flex: 1;
}

.admin-seller-request-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-spacing-2) 0;
}

.admin-seller-request-meta {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-3);
  flex-wrap: wrap;
}

.admin-seller-request-date {
  color: var(--admin-text-secondary);
  font-size: 0.9rem;
}

.admin-seller-request-actions {
  flex-shrink: 0;
}

.admin-seller-request-status-actions {
  display: flex;
  gap: var(--admin-spacing-2);
}

/* Schedule Table */
.admin-schedule-table {
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  overflow: hidden;
  background: var(--admin-white);
}

.admin-schedule-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 120px;
  background: var(--admin-gray-50);
  border-bottom: 1px solid var(--admin-border-color);
}

.admin-schedule-header-cell {
  padding: var(--admin-spacing-3);
  font-weight: 600;
  color: var(--admin-text-primary);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-schedule-body {
  display: flex;
  flex-direction: column;
}

.admin-schedule-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 120px;
  border-bottom: 1px solid var(--admin-border-color);
  transition: background-color 0.2s ease;
}

.admin-schedule-row:last-child {
  border-bottom: none;
}

.admin-schedule-row:hover {
  background: var(--admin-gray-25);
}

.admin-schedule-cell {
  padding: var(--admin-spacing-3);
  display: flex;
  align-items: center;
  color: var(--admin-text-primary);
}

.admin-schedule-day {
  font-weight: 500;
}

@media (max-width: 768px) {
  .admin-schedule-header,
  .admin-schedule-row {
    grid-template-columns: 1fr;
  }

  .admin-schedule-header-cell,
  .admin-schedule-cell {
    padding: var(--admin-spacing-2);
  }

  .admin-schedule-row {
    border: 1px solid var(--admin-border-color);
    border-radius: var(--admin-border-radius);
    margin-bottom: var(--admin-spacing-2);
    background: var(--admin-white);
  }

  .admin-schedule-header {
    display: none;
  }

  .admin-schedule-cell:before {
    content: attr(data-label);
    font-weight: 600;
    color: var(--admin-text-secondary);
    margin-right: var(--admin-spacing-2);
    min-width: 80px;
  }
}

/* User Avatar Section */
.admin-user-avatar-section {
  text-align: center;
  margin-bottom: var(--admin-spacing-4);
}

.admin-user-avatar-large {
  width: 80px;
  height: 80px;
  font-size: 24px;
  margin: 0 auto;
}

/* Info Groups */
.admin-info-group {
  margin-bottom: var(--admin-spacing-3);
}

.admin-info-group-full {
  grid-column: span 2;
}

.admin-info-label {
  display: block;
  font-weight: 600;
  color: var(--admin-text-secondary);
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--admin-spacing-1);
}

.admin-info-value {
  color: var(--admin-text-primary);
  font-weight: 500;
}

.admin-info-value-multiline {
  line-height: 1.5;
  white-space: pre-wrap;
}

/* Image Preview */
.admin-image-preview {
  max-width: 200px;
}

.admin-image-preview-img {
  width: 100%;
  height: auto;
  border-radius: var(--admin-border-radius);
  border: 1px solid var(--admin-border-color);
}

/* Spacing utilities */
.admin-mt-4 {
  margin-top: var(--admin-spacing-4);
}

/* Responsive */
@media (max-width: 1024px) {
  .admin-grid-seller-request {
    grid-template-columns: 1fr;
    grid-template-areas:
      "user"
      "company"
      "finance"
      "schedule"
      "additional"
      "documents"
      "rejection";
  }
}

@media (max-width: 768px) {
  .admin-grid-seller-request {
    grid-template-columns: 1fr;
    gap: var(--admin-spacing-3);
  }

  .admin-form-grid-2 {
    grid-template-columns: 1fr;
  }

  .admin-info-group-full {
    grid-column: span 1;
  }

  .admin-seller-request-header {
    flex-direction: column;
    gap: var(--admin-spacing-3);
  }

  .admin-seller-request-actions {
    width: 100%;
  }

  .admin-seller-request-status-actions {
    display: flex;
    gap: var(--admin-spacing-2);
    width: 100%;
  }

  .admin-seller-request-status-actions .admin-btn {
    flex: 1;
  }
}

.seller-request-detail {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.py-4 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.request-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.request-subtitle {
  font-size: 1rem;
  color: #7a7a7a;
}

.user-avatar {
  width: 150px;
  height: 150px;
  margin: 0 auto 1.5rem;
  border-radius: 50%;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.info-group {
  margin-bottom: 1.5rem;
}

.info-group:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #7a7a7a;
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 1rem;
}

.social-links {
  display: flex;
  flex-wrap: wrap;
}

.documents-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.document-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.document-icon {
  margin-right: 1rem;
  font-size: 1.5rem;
  color: #7a7a7a;
}

.document-info {
  flex: 1;
}

.document-name {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.document-type {
  font-size: 0.8rem;
  color: #7a7a7a;
}

.document-actions {
  margin-left: 1rem;
}

/* Info Groups */
.admin-info-group {
  margin-bottom: var(--admin-spacing-3);
}

.admin-info-group-full {
  grid-column: span 2;
}

.admin-info-label {
  display: block;
  font-weight: 600;
  color: var(--admin-text-secondary);
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--admin-spacing-1);
}

.admin-info-value {
  color: var(--admin-text-primary);
  font-weight: 500;
}

.admin-info-value-multiline {
  line-height: 1.5;
  white-space: pre-wrap;
}

/* Grid Layout */
.admin-grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--admin-spacing-4);
}

/* Spacing utilities */
.admin-mt-4 {
  margin-top: var(--admin-spacing-4);
}

/* Empty State */
.admin-empty-state {
  text-align: center;
  padding: var(--admin-spacing-6);
  color: var(--admin-text-secondary);
}

.admin-empty-icon {
  font-size: 48px;
  margin-bottom: var(--admin-spacing-3);
  opacity: 0.5;
}

.admin-empty-text {
  font-size: 16px;
  margin: 0;
}

/* Documents */
.admin-documents-list {
  display: flex;
  flex-direction: column;
  gap: var(--admin-spacing-3);
}

.admin-document-item {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-3);
  padding: var(--admin-spacing-3);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  background: var(--admin-bg-secondary);
}

.admin-document-icon {
  font-size: 24px;
  color: var(--admin-primary);
  min-width: 40px;
  text-align: center;
}

.admin-document-info {
  flex: 1;
}

.admin-document-title {
  font-weight: 600;
  margin: 0 0 var(--admin-spacing-1) 0;
  color: var(--admin-text-primary);
}

.admin-document-type {
  font-size: 14px;
  color: var(--admin-text-secondary);
  margin: 0;
  text-transform: capitalize;
}

.admin-document-actions {
  margin-left: auto;
}

/* Card Variants */
.admin-card-danger {
  border-left: 4px solid var(--admin-danger);
}

.admin-card-danger .admin-card-title {
  color: var(--admin-danger);
}

/* Responsive */
@media (max-width: 1024px) {
  .admin-grid-3 {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .admin-grid-3 {
    grid-template-columns: 1fr;
  }

  .admin-form-grid-2 {
    grid-template-columns: 1fr;
  }

  .admin-info-group-full {
    grid-column: span 1;
  }

  .admin-document-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .admin-document-actions {
    margin-left: 0;
    align-self: stretch;
  }
}
</style>
