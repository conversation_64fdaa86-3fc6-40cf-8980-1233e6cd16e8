<template>
  <div class="admin-page">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <button @click="goBack" class="admin-btn admin-btn-ghost admin-btn-sm">
          <i class="fas fa-arrow-left"></i>
          Back to Companies
        </button>
        <h1 class="admin-page-title">
          <i class="fas fa-building admin-page-icon"></i>
          Edit Company
        </h1>
        <p class="admin-page-subtitle">Update company information and settings</p>
      </div>
      <div class="admin-page-actions">
        <button @click="resetForm" class="admin-btn admin-btn-secondary">
          <i class="fas fa-undo"></i>
          Reset
        </button>
        <button @click="updateCompany" :disabled="saving" class="admin-btn admin-btn-primary">
          <i class="fas fa-save"></i>
          {{ saving ? 'Saving...' : 'Save Changes' }}
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="admin-loading-container">
      <div class="admin-loading-spinner"></div>
      <p class="admin-loading-text">Loading company data...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="admin-alert admin-alert-error">
      <i class="fas fa-exclamation-triangle"></i>
      {{ error }}
    </div>

    <!-- Main Content -->
    <div v-else-if="company" class="admin-page-content">
      <form @submit.prevent="updateCompany" class="admin-form">
        <!-- Basic Information Card -->
        <div class="admin-card">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-info-circle"></i>
              Basic Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-row">
              <div class="admin-form-group">
                <label class="admin-form-label">Company Name *</label>
                <input
                  class="admin-form-input"
                  type="text"
                  v-model="form.name"
                  placeholder="Enter company name"
                  required
                >
              </div>
              <div class="admin-form-group">
                <label class="admin-form-label">Contact Email *</label>
                <input
                  class="admin-form-input"
                  type="email"
                  v-model="form.contactEmail"
                  placeholder="<EMAIL>"
                  required
                >
              </div>
            </div>

            <div class="admin-form-group">
              <label class="admin-form-label">Description</label>
              <textarea
                class="admin-form-textarea"
                v-model="form.description"
                rows="4"
                placeholder="Company description..."
              ></textarea>
            </div>
          </div>
        </div>

        <!-- Contact Information Card -->
        <div class="admin-card">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-phone"></i>
              Contact Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-row">
              <div class="admin-form-group">
                <label class="admin-form-label">Contact Phone</label>
                <input
                  class="admin-form-input"
                  type="tel"
                  v-model="form.contactPhone"
                  placeholder="+380 XX XXX XXXX"
                >
              </div>
              <div class="admin-form-group">
                <label class="admin-form-label">Address</label>
                <input
                  class="admin-form-input"
                  type="text"
                  v-model="form.address"
                  placeholder="Company address"
                >
              </div>
            </div>
          </div>
        </div>

        <!-- SEO Information Card -->
        <div class="admin-card">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-search"></i>
              SEO Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-group">
              <label class="admin-form-label">Meta Title</label>
              <input
                class="admin-form-input"
                type="text"
                v-model="form.metaTitle"
                placeholder="SEO meta title"
              >
            </div>

            <div class="admin-form-group">
              <label class="admin-form-label">Meta Description</label>
              <textarea
                class="admin-form-textarea"
                v-model="form.metaDescription"
                rows="3"
                placeholder="SEO meta description..."
              ></textarea>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="admin-form-actions">
          <button type="button" @click="goBack" class="admin-btn admin-btn-secondary">
            <i class="fas fa-times"></i>
            Cancel
          </button>
          <button type="submit" :disabled="saving" class="admin-btn admin-btn-primary">
            <i class="fas fa-save"></i>
            {{ saving ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>

import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { companiesService } from '@/admin/services/companies';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(true);
const saving = ref(false);
const error = ref('');
const company = ref(null);


// Form data
const form = reactive({
  name: '',
  description: '',
  contactEmail: '',
  contactPhone: '',
  address: '',
  metaTitle: '',
  metaDescription: ''
});



// Methods
const fetchCompany = async () => {
  try {
    loading.value = true;
    error.value = '';

    const response = await companiesService.getDetailedCompany(route.params.id);
    company.value = response.data;

    // Populate form
    Object.assign(form, {
      name: company.value.name || '',
      description: company.value.description || '',
      contactEmail: company.value.contactEmail || '',
      contactPhone: company.value.contactPhone || '',
      address: company.value.address || '',
      metaTitle: company.value.metaTitle || '',
      metaDescription: company.value.metaDescription || ''
    });



  } catch (err) {
    error.value = err.message || 'Failed to load company';
  } finally {
    loading.value = false;
  }
};

const handleImageUpload = (event) => {
  const file = event.target.files[0];
  if (file) {
    imageFileName.value = file.name;
    // Here you would typically upload the file to your server
    // For now, we'll just create a local URL
    const reader = new FileReader();
    reader.onload = (e) => {
      form.imageUrl = e.target.result;
    };
    reader.readAsDataURL(file);
  }
};

const updateCompany = async () => {
  try {
    saving.value = true;
    error.value = '';

    await companiesService.updateDetailedCompany(route.params.id, form);

    // Redirect back to companies list
    router.push('/admin/companies');

  } catch (err) {
    error.value = err.message || 'Failed to update company';
  } finally {
    saving.value = false;
  }
};

// Navigation methods
const goBack = () => {
  router.push('/admin/companies');
};

const resetForm = () => {
  if (company.value) {
    // Reset form to original company data
    Object.assign(form, {
      name: company.value.name || '',
      description: company.value.description || '',
      contactEmail: company.value.contactEmail || '',
      contactPhone: company.value.contactPhone || '',
      address: company.value.address || '',
      metaTitle: company.value.metaTitle || '',
      metaDescription: company.value.metaDescription || ''
    });
  }
};

// Lifecycle
onMounted(() => {
  fetchCompany();
});
</script>
    loadingProducts.value = true;
    const response = await productsService.getProducts({
      companyId: route.params.id,
      page: 1,
      limit: 100 // Get all products for this company
    });
    products.value = response.data || [];
  } catch (err) {
    console.error('Error fetching products:', err);
    products.value = [];
  } finally {
    loadingProducts.value = false;
  }
};

const deleteProduct = async (productId) => {
  if (!confirm('Are you sure you want to delete this product?')) {
    return;
  }

  try {
    deletingProduct.value = productId;
    await productsService.deleteProduct(productId);

    // Remove from local list
    products.value = products.value.filter(p => p.id !== productId);
  } catch (err) {
    console.error('Error deleting product:', err);
    alert('Failed to delete product: ' + (err.message || 'Unknown error'));
  } finally {
    deletingProduct.value = null;
  }
};

// Utility methods for products
const formatPrice = (amount, currency) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD'
  }).format(amount || 0);
};

const getStockClass = (stock) => {
  if (stock === 0) return 'is-danger';
  if (stock < 10) return 'is-warning';
  return 'is-success';
};

const getStatusClass = (status) => {
  switch (status) {
    case 0: return 'is-warning'; // Pending
    case 1: return 'is-success'; // Approved
    case 2: return 'is-danger';  // Rejected
    default: return 'is-light';
  }
};

const getStatusText = (status) => {
  switch (status) {
    case 0: return 'Pending';
    case 1: return 'Approved';
    case 2: return 'Rejected';
    default: return 'Unknown';
  }
};

// Navigation methods
const goBack = () => {
  router.push('/admin/companies');
};

const resetForm = () => {
  if (company.value) {
    // Reset form to original company data
    Object.assign(form, {
      name: company.value.name || '',
      description: company.value.description || '',
      contactEmail: company.value.contactEmail || '',
      contactPhone: company.value.contactPhone || '',
      address: company.value.address || '',
      metaTitle: company.value.metaTitle || '',
      metaDescription: company.value.metaDescription || ''
    });

    // Reset finance form
    Object.assign(financeForm, {
      bankName: company.value.finance?.bankName || '',
      bankAccount: company.value.finance?.bankAccount || '',
      bankCode: company.value.finance?.bankCode || '',
      paymentDetails: company.value.finance?.paymentDetails || ''
    });

    // Reset schedule form
    if (company.value.schedule && company.value.schedule.length > 0) {
      scheduleForm.value = company.value.schedule.map(s => ({
        day: s.day,
        openTime: s.openTime || '09:00',
        closeTime: s.closeTime || '18:00',
        isClosed: s.isClosed || false
      }));
    } else {
      scheduleForm.value = Array.from({ length: 7 }, (_, index) => ({
        day: index,
        openTime: '09:00',
        closeTime: '18:00',
        isClosed: false
      }));
    }
  }
};

// Lifecycle
onMounted(() => {
  fetchCompany();
  fetchProducts();
});
</script>

<style scoped>
.company-edit {
  padding: 1rem;
}

.subtitle {
  margin-top: 2rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #dbdbdb;
  padding-bottom: 0.5rem;
}

.image img {
  object-fit: cover;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #ff7700;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
