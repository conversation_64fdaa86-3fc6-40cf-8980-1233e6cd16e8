<template>
  <div class="company-edit">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Edit Company</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <router-link to="/admin/companies" class="button">
            <span class="icon">
              <i class="fas fa-arrow-left"></i>
            </span>
            <span>Back to Companies</span>
          </router-link>
        </div>
      </div>
    </div>

    <div v-if="loading" class="has-text-centered py-6">
      <div class="is-loading"></div>
      <p>Loading company...</p>
    </div>

    <div v-else-if="error" class="notification is-danger">
      <p>{{ error }}</p>
    </div>

    <div v-else-if="company" class="box">
      <form @submit.prevent="updateCompany">
        <div class="columns">
          <div class="column is-8">
            <!-- Basic Information -->
            <div class="field">
              <label class="label">Company Name *</label>
              <div class="control">
                <input
                  v-model="form.name"
                  class="input"
                  type="text"
                  placeholder="Enter company name"
                  required>
              </div>
            </div>

            <div class="field">
              <label class="label">Description</label>
              <div class="control">
                <textarea
                  v-model="form.description"
                  class="textarea"
                  placeholder="Enter company description"
                  rows="4"></textarea>
              </div>
            </div>

            <!-- Contact Information -->
            <h3 class="subtitle">Contact Information</h3>
            
            <div class="columns">
              <div class="column">
                <div class="field">
                  <label class="label">Contact Email *</label>
                  <div class="control">
                    <input
                      v-model="form.contactEmail"
                      class="input"
                      type="email"
                      placeholder="<EMAIL>"
                      required>
                  </div>
                </div>
              </div>
              <div class="column">
                <div class="field">
                  <label class="label">Contact Phone</label>
                  <div class="control">
                    <input
                      v-model="form.contactPhone"
                      class="input"
                      type="tel"
                      placeholder="+380XXXXXXXXX">
                  </div>
                </div>
              </div>
            </div>

            <!-- Address -->
            <h3 class="subtitle">Address</h3>
            
            <div class="columns">
              <div class="column">
                <div class="field">
                  <label class="label">Region</label>
                  <div class="control">
                    <input
                      v-model="form.addressRegion"
                      class="input"
                      type="text"
                      placeholder="Region">
                  </div>
                </div>
              </div>
              <div class="column">
                <div class="field">
                  <label class="label">City</label>
                  <div class="control">
                    <input
                      v-model="form.addressCity"
                      class="input"
                      type="text"
                      placeholder="City">
                  </div>
                </div>
              </div>
            </div>

            <div class="columns">
              <div class="column">
                <div class="field">
                  <label class="label">Street</label>
                  <div class="control">
                    <input
                      v-model="form.addressStreet"
                      class="input"
                      type="text"
                      placeholder="Street address">
                  </div>
                </div>
              </div>
              <div class="column is-4">
                <div class="field">
                  <label class="label">Postal Code</label>
                  <div class="control">
                    <input
                      v-model="form.addressPostalCode"
                      class="input"
                      type="text"
                      placeholder="12345">
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="column is-4">
            <!-- Company Image -->
            <div class="field">
              <label class="label">Company Image</label>
              <div class="control">
                <div class="file has-name is-fullwidth">
                  <label class="file-label">
                    <input class="file-input" type="file" @change="handleImageUpload" accept="image/*">
                    <span class="file-cta">
                      <span class="file-icon">
                        <i class="fas fa-upload"></i>
                      </span>
                      <span class="file-label">Choose image...</span>
                    </span>
                    <span class="file-name">{{ imageFileName || 'No file selected' }}</span>
                  </label>
                </div>
              </div>
              <div v-if="form.imageUrl" class="mt-3">
                <figure class="image is-128x128">
                  <img :src="form.imageUrl" alt="Company image" class="is-rounded">
                </figure>
              </div>
            </div>

            <!-- Featured Status -->
            <div class="field">
              <div class="control">
                <label class="checkbox">
                  <input v-model="form.isFeatured" type="checkbox">
                  Featured Company
                </label>
              </div>
              <p class="help">Featured companies appear prominently on the site</p>
            </div>
          </div>
        </div>

        <!-- Finance Information -->
        <div class="box mt-5">
          <h3 class="subtitle">Finance Information</h3>
          <div class="columns">
            <div class="column">
              <div class="field">
                <label class="label">Bank Name</label>
                <div class="control">
                  <input
                    v-model="financeForm.bankName"
                    class="input"
                    type="text"
                    placeholder="Enter bank name">
                </div>
              </div>
              <div class="field">
                <label class="label">Bank Account</label>
                <div class="control">
                  <input
                    v-model="financeForm.bankAccount"
                    class="input"
                    type="text"
                    placeholder="Enter bank account number">
                </div>
              </div>
            </div>
            <div class="column">
              <div class="field">
                <label class="label">Bank Code</label>
                <div class="control">
                  <input
                    v-model="financeForm.bankCode"
                    class="input"
                    type="text"
                    placeholder="Enter bank code">
                </div>
              </div>
              <div class="field">
                <label class="label">Tax ID</label>
                <div class="control">
                  <input
                    v-model="financeForm.taxId"
                    class="input"
                    type="text"
                    placeholder="Enter tax ID">
                </div>
              </div>
            </div>
          </div>
          <div class="field">
            <label class="label">Payment Details</label>
            <div class="control">
              <textarea
                v-model="financeForm.paymentDetails"
                class="textarea"
                placeholder="Enter payment details"
                rows="3"></textarea>
            </div>
          </div>
        </div>

        <!-- Schedule Information -->
        <div class="box mt-5">
          <h3 class="subtitle">Schedule Information</h3>
          <div class="table-container">
            <table class="table is-fullwidth">
              <thead>
                <tr>
                  <th>Day</th>
                  <th>Open Time</th>
                  <th>Close Time</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(schedule, index) in scheduleForm" :key="schedule.day">
                  <td>{{ getDayName(schedule.day) }}</td>
                  <td>
                    <input
                      v-model="schedule.openTime"
                      class="input"
                      type="time"
                      :disabled="schedule.isClosed">
                  </td>
                  <td>
                    <input
                      v-model="schedule.closeTime"
                      class="input"
                      type="time"
                      :disabled="schedule.isClosed">
                  </td>
                  <td>
                    <label class="checkbox">
                      <input v-model="schedule.isClosed" type="checkbox">
                      Closed
                    </label>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Company Products -->
        <div class="box mt-5">
          <h3 class="subtitle">Company Products</h3>

          <div v-if="loadingProducts" class="has-text-centered">
            <div class="loader"></div>
            <p>Loading products...</p>
          </div>

          <div v-else-if="products.length === 0" class="has-text-centered">
            <p class="has-text-grey">No products found for this company.</p>
          </div>

          <div v-else class="table-container">
            <table class="table is-fullwidth is-striped">
              <thead>
                <tr>
                  <th>Image</th>
                  <th>Name</th>
                  <th>Category</th>
                  <th>Price</th>
                  <th>Stock</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="product in products" :key="product.id">
                  <td>
                    <figure class="image is-48x48">
                      <img
                        :src="product.imageUrl || '/placeholder-product.jpg'"
                        :alt="product.name"
                        class="is-rounded">
                    </figure>
                  </td>
                  <td>
                    <strong>{{ product.name }}</strong>
                    <br>
                    <small class="has-text-grey">{{ product.slug }}</small>
                  </td>
                  <td>{{ product.categoryName || 'N/A' }}</td>
                  <td>
                    <span class="tag is-info">
                      {{ formatPrice(product.priceAmount, product.priceCurrency) }}
                    </span>
                  </td>
                  <td>
                    <span class="tag" :class="getStockClass(product.stock)">
                      {{ product.stock }}
                    </span>
                  </td>
                  <td>
                    <span class="tag" :class="getStatusClass(product.status)">
                      {{ getStatusText(product.status) }}
                    </span>
                  </td>
                  <td>
                    <div class="buttons are-small">
                      <router-link
                        :to="`/admin/products/${product.id}`"
                        class="button is-info is-small">
                        <span class="icon"><i class="fas fa-eye"></i></span>
                        <span>View</span>
                      </router-link>
                      <router-link
                        :to="`/admin/products/${product.id}/edit`"
                        class="button is-warning is-small">
                        <span class="icon"><i class="fas fa-edit"></i></span>
                        <span>Edit</span>
                      </router-link>
                      <button
                        @click="deleteProduct(product.id)"
                        class="button is-danger is-small"
                        :disabled="deletingProduct === product.id">
                        <span class="icon">
                          <i class="fas fa-trash" v-if="deletingProduct !== product.id"></i>
                          <i class="fas fa-spinner fa-spin" v-else></i>
                        </span>
                        <span>Delete</span>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Actions -->
        <div class="field is-grouped mt-5">
          <div class="control">
            <button
              type="submit"
              class="button is-primary"
              :class="{ 'is-loading': saving }"
              :disabled="saving">
              <span class="icon">
                <i class="fas fa-save"></i>
              </span>
              <span>Save Changes</span>
            </button>
          </div>
          <div class="control">
            <router-link to="/admin/companies" class="button">
              Cancel
            </router-link>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { companiesService } from '@/admin/services/companies';
import { productsService } from '@/admin/services/products';

const route = useRoute();
const router = useRouter();

// State
const loading = ref(true);
const saving = ref(false);
const error = ref('');
const company = ref(null);
const imageFileName = ref('');

// Products state
const products = ref([]);
const loadingProducts = ref(false);
const deletingProduct = ref(null);

// Form data
const form = reactive({
  name: '',
  description: '',
  contactEmail: '',
  contactPhone: '',
  addressRegion: '',
  addressCity: '',
  addressStreet: '',
  addressPostalCode: '',
  imageUrl: '',
  isFeatured: false
});

// Finance form data
const financeForm = reactive({
  bankName: '',
  bankAccount: '',
  bankCode: '',
  taxId: '',
  paymentDetails: ''
});

// Schedule form data
const scheduleForm = ref([]);

// Methods
const fetchCompany = async () => {
  try {
    loading.value = true;
    error.value = '';

    const response = await companiesService.getDetailedCompany(route.params.id);
    company.value = response.data;

    // Populate form
    Object.assign(form, {
      name: company.value.name || '',
      description: company.value.description || '',
      contactEmail: company.value.contactEmail || '',
      contactPhone: company.value.contactPhone || '',
      addressRegion: company.value.addressRegion || '',
      addressCity: company.value.addressCity || '',
      addressStreet: company.value.addressStreet || '',
      addressPostalCode: company.value.addressPostalCode || '',
      imageUrl: company.value.imageUrl || '',
      isFeatured: company.value.isFeatured || false
    });

    // Populate finance form if exists
    if (company.value.finance) {
      Object.assign(financeForm, {
        bankName: company.value.finance.bankName || '',
        bankAccount: company.value.finance.bankAccount || '',
        bankCode: company.value.finance.bankCode || '',
        taxId: company.value.finance.taxId || '',
        paymentDetails: company.value.finance.paymentDetails || ''
      });
    }

    // Populate schedule form if exists
    if (company.value.schedule && company.value.schedule.length > 0) {
      scheduleForm.value = company.value.schedule.map(s => ({
        day: s.day,
        openTime: s.openTime || '09:00',
        closeTime: s.closeTime || '18:00',
        isClosed: s.isClosed || false
      }));
    } else {
      // Initialize default schedule for all days
      scheduleForm.value = Array.from({ length: 7 }, (_, index) => ({
        day: index,
        openTime: '09:00',
        closeTime: '18:00',
        isClosed: false
      }));
    }

  } catch (err) {
    error.value = err.message || 'Failed to load company';
  } finally {
    loading.value = false;
  }
};

const handleImageUpload = (event) => {
  const file = event.target.files[0];
  if (file) {
    imageFileName.value = file.name;
    // Here you would typically upload the file to your server
    // For now, we'll just create a local URL
    const reader = new FileReader();
    reader.onload = (e) => {
      form.imageUrl = e.target.result;
    };
    reader.readAsDataURL(file);
  }
};

const updateCompany = async () => {
  try {
    saving.value = true;
    error.value = '';

    // Prepare data for update
    const updateData = {
      company: form,
      finance: financeForm,
      schedule: scheduleForm.value
    };

    await companiesService.updateDetailedCompany(route.params.id, updateData);

    // Redirect back to companies list
    router.push('/admin/companies');

  } catch (err) {
    error.value = err.message || 'Failed to update company';
  } finally {
    saving.value = false;
  }
};

const getDayName = (dayNumber) => {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days[dayNumber] || 'Unknown';
};

// Products methods
const fetchProducts = async () => {
  try {
    loadingProducts.value = true;
    const response = await productsService.getProducts({
      companyId: route.params.id,
      page: 1,
      limit: 100 // Get all products for this company
    });
    products.value = response.data || [];
  } catch (err) {
    console.error('Error fetching products:', err);
    products.value = [];
  } finally {
    loadingProducts.value = false;
  }
};

const deleteProduct = async (productId) => {
  if (!confirm('Are you sure you want to delete this product?')) {
    return;
  }

  try {
    deletingProduct.value = productId;
    await productsService.deleteProduct(productId);

    // Remove from local list
    products.value = products.value.filter(p => p.id !== productId);
  } catch (err) {
    console.error('Error deleting product:', err);
    alert('Failed to delete product: ' + (err.message || 'Unknown error'));
  } finally {
    deletingProduct.value = null;
  }
};

// Utility methods for products
const formatPrice = (amount, currency) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD'
  }).format(amount || 0);
};

const getStockClass = (stock) => {
  if (stock === 0) return 'is-danger';
  if (stock < 10) return 'is-warning';
  return 'is-success';
};

const getStatusClass = (status) => {
  switch (status) {
    case 0: return 'is-warning'; // Pending
    case 1: return 'is-success'; // Approved
    case 2: return 'is-danger';  // Rejected
    default: return 'is-light';
  }
};

const getStatusText = (status) => {
  switch (status) {
    case 0: return 'Pending';
    case 1: return 'Approved';
    case 2: return 'Rejected';
    default: return 'Unknown';
  }
};

// Lifecycle
onMounted(() => {
  fetchCompany();
  fetchProducts();
});
</script>

<style scoped>
.company-edit {
  padding: 1rem;
}

.subtitle {
  margin-top: 2rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #dbdbdb;
  padding-bottom: 0.5rem;
}

.image img {
  object-fit: cover;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #ff7700;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
