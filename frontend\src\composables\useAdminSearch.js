import { ref, reactive, computed, watch } from 'vue';

/**
 * Універсальний composable для пошуку та фільтрації в адмінці
 * @param {Object} options - Опції конфігурації
 * @param {Function} options.fetchFunction - Функція для отримання даних з API
 * @param {Object} options.defaultFilters - Дефолтні фільтри
 * @param {number} options.debounceTime - Час затримки для дебаунсу (мс)
 * @param {number} options.defaultPageSize - Розмір сторінки за замовчуванням
 * @param {boolean} options.clientSideSearch - Чи виконувати пошук на клієнті
 * @param {boolean} options.enableVirtualScrolling - Увімкнути віртуальний скролінг
 * @param {number} options.virtualScrollThreshold - Поріг для віртуального скролінгу
 * @param {boolean} options.enableInfiniteScroll - Увімкнути нескінченний скролінг
 * @param {boolean} options.enableOptimisticUpdates - Увімкнути оптимістичні оновлення
 */
export function useAdminSearch(options = {}) {
  const {
    fetchFunction,
    defaultFilters = {},
    debounceTime = 300,
    defaultPageSize = 15,
    clientSideSearch = false,
    enableVirtualScrolling = false,
    virtualScrollThreshold = 100,
    enableInfiniteScroll = false,
    enableOptimisticUpdates = false
  } = options;

  // Стан даних
  const items = ref([]);
  const loading = ref(false);
  const error = ref(null);
  const isFirstLoad = ref(true);
  const lastFetchTime = ref(0);
  const requestId = ref(0);

  // Пагінація
  const currentPage = ref(1);
  const totalPages = ref(1);
  const totalItems = ref(0);
  const pageSize = ref(defaultPageSize);

  // Продуктивність
  const isLoadingMore = ref(false); // Для нескінченного скролінгу
  const allItemsLoaded = ref(false); // Чи всі елементи завантажені
  const optimisticUpdates = ref(new Map()); // Оптимістичні оновлення
  const pendingRequests = ref(new Set()); // Відстеження запитів що виконуються

  // Фільтри
  const filters = reactive({
    search: '',
    ...defaultFilters
  });

  // Сортування
  const sortBy = ref('createdAt');
  const sortOrder = ref('desc');

  // Computed properties
  const hasFilters = computed(() => {
    return Object.values(filters).some(value => 
      value !== '' && value !== null && value !== undefined
    );
  });

  const activeFilters = computed(() => {
    return Object.entries(filters)
      .filter(([_, value]) => value !== '' && value !== null && value !== undefined)
      .reduce((acc, [key, value]) => {
        acc[key] = value;
        return acc;
      }, {});
  });

  // Функція для отримання даних
  const fetchData = async (page = 1, options = {}) => {
    const {
      append = false, // Для нескінченного скролінгу
      force = false,  // Примусове оновлення
      silent = false  // Тихе оновлення без індикатора
    } = options;

    // Генеруємо унікальний ID запиту
    const currentRequestId = ++requestId.value;

    // Перевіряємо чи не дублюємо запит
    const requestKey = `${page}-${JSON.stringify(activeFilters.value)}`;
    if (pendingRequests.value.has(requestKey) && !force) {
      console.log('🔄 Duplicate request prevented:', requestKey);
      return;
    }

    // Перевіряємо кеш (якщо не примусове оновлення)
    const cacheKey = `${page}-${JSON.stringify(activeFilters.value)}-${sortBy.value}-${sortOrder.value}`;
    const now = Date.now();
    const cacheTimeout = 30000; // 30 секунд

    if (!force && lastFetchTime.value && (now - lastFetchTime.value) < cacheTimeout) {
      console.log('📦 Using cached data');
      return;
    }

    pendingRequests.value.add(requestKey);

    if (!silent) {
      if (append) {
        isLoadingMore.value = true;
      } else if (isFirstLoad.value || currentPage.value !== page) {
        loading.value = true;
      }
    }

    if (!append) {
      currentPage.value = page;
    }
    error.value = null;

    try {
      // Підготовка параметрів для API
      const apiParams = {
        page: currentPage.value,
        pageSize: pageSize.value
      };

      // Додаємо пошук як filter та search параметри
      if (filters.search && filters.search.trim()) {
        apiParams.filter = filters.search.trim();
        apiParams.search = filters.search.trim();
      }

      // Додаємо фільтри
      if (!clientSideSearch) {
        // Серверний пошук - додаємо всі фільтри до API запиту
        Object.entries(activeFilters.value).forEach(([key, value]) => {
          if (key === 'search') {
            // Пошук вже додано як filter
            return;
          } else if (key === 'sortBy') {
            // Передаємо sortBy як є для usersService
            apiParams.sortBy = value;
          } else if (key === 'sortOrder') {
            // Передаємо sortOrder як є для usersService
            apiParams.sortOrder = value;
          } else if (key === 'status') {
            // Конвертуємо статус в числове значення для backend
            console.log('🔄 Converting status filter:', value);
            apiParams.status = value;
          } else if (key === 'categoryId') {
            // Передаємо categoryId як є
            apiParams.categoryId = value;
          } else if (key === 'categoryIds') {
            // Передаємо масив ID категорій (включаючи субкатегорії)
            if (Array.isArray(value) && value.length > 0) {
              apiParams.categoryIds = value.join(',');
            }
          } else if (key === 'stock') {
            // Передаємо stock як є (in-stock, low-stock, out-of-stock)
            apiParams.stock = value;
          } else {
            apiParams[key] = value;
          }
        });
      } else {
        // Клієнтський пошук - додаємо тільки не-пошукові фільтри
        Object.entries(activeFilters.value).forEach(([key, value]) => {
          if (key !== 'search') {
            if (key === 'sortBy') {
              // Передаємо sortBy як є для usersService
              apiParams.sortBy = value;
            } else if (key === 'sortOrder') {
              // Передаємо sortOrder як є для usersService
              apiParams.sortOrder = value;
            } else if (key === 'status') {
              console.log('🔄 Converting status filter (client-side):', value);
              apiParams.status = value;
            } else if (key === 'categoryId') {
              apiParams.categoryId = value;
            } else if (key === 'categoryIds') {
              if (Array.isArray(value) && value.length > 0) {
                apiParams.categoryIds = value.join(',');
              }
            } else if (key === 'stock') {
              apiParams.stock = value;
            } else {
              apiParams[key] = value;
            }
          }
        });
      }

      console.log('🔍 useAdminSearch: Fetching data with params:', apiParams);

      const response = await fetchFunction(apiParams);

      // Перевіряємо чи запит ще актуальний
      if (currentRequestId !== requestId.value) {
        console.log('🚫 Request outdated, ignoring response');
        return;
      }

      // Обробка відповіді
      let processedItems = [];
      let paginationData = {};

      console.log('Processing response:', response);

      if (response) {
        // Варіант 1: Our updated service format with data and pagination
        if (response.data && Array.isArray(response.data) && response.pagination) {
          console.log('Using updated service format with pagination');
          processedItems = response.data;
          paginationData = {
            total: response.pagination.total || response.total,
            page: response.pagination.page || response.currentPage,
            totalPages: response.pagination.totalPages || response.totalPages,
            perPage: response.pagination.pageSize || response.pagination.limit
          };
        }
        // Варіант 2: Direct data array with separate pagination properties
        else if (response.data && Array.isArray(response.data) && (response.total !== undefined || response.totalPages !== undefined)) {
          console.log('Using data array with separate pagination');
          processedItems = response.data;
          paginationData = {
            total: response.total || response.totalItems,
            page: response.currentPage || response.page,
            totalPages: response.totalPages || response.lastPage,
            perPage: response.pageSize || response.perPage
          };
        }
        // Варіант 3: Categories service format
        else if (response.categories && Array.isArray(response.categories)) {
          console.log('Using categories service format');
          processedItems = response.categories;
          paginationData = {
            total: response.totalCount || response.total,
            page: 1,
            totalPages: 1,
            perPage: processedItems.length
          };
        }
        // Варіант 4: Items array format
        else if (response.items && Array.isArray(response.items)) {
          console.log('Using items array format');
          processedItems = response.items;
          paginationData = {
            total: response.total || response.totalItems || processedItems.length,
            page: response.currentPage || response.page || 1,
            totalPages: response.totalPages || response.lastPage || 1,
            perPage: response.pageSize || response.perPage || processedItems.length
          };
        }
        // Варіант 5: ApiResponse<PaginatedResponse> format
        else if (response.success && response.data && response.data.data) {
          console.log('Using ApiResponse<PaginatedResponse> format');
          processedItems = response.data.data;
          paginationData = {
            total: response.data.total,
            page: response.data.currentPage,
            totalPages: response.data.lastPage,
            perPage: response.data.perPage
          };
        }
        // Варіант 6: Users service format
        else if (response.users) {
          console.log('Using Users service format');
          processedItems = response.users;
          paginationData = response.pagination || {};
        }
        // Варіант 7: Direct array
        else if (Array.isArray(response)) {
          console.log('Using direct array format');
          processedItems = response;
          paginationData = {
            total: response.length,
            page: 1,
            totalPages: 1,
            perPage: response.length
          };
        }
        // Варіант 8: Legacy format with data property
        else if (response.data) {
          console.log('Using legacy data format');
          if (Array.isArray(response.data)) {
            processedItems = response.data;
            paginationData = response.pagination || {
              total: response.data.length,
              page: 1,
              totalPages: 1,
              perPage: response.data.length
            };
          } else if (response.data.data) {
            processedItems = response.data.data;
            paginationData = response.data;
          }
        }
        else {
          console.warn('Unknown response format:', response);
          processedItems = [];
          paginationData = {};
        }
      }

      // Клієнтський пошук
      if (clientSideSearch && filters.search) {
        const searchQuery = filters.search.toLowerCase();
        processedItems = processedItems.filter(item => {
          return searchInItem(item, searchQuery);
        });
      }

      items.value = processedItems;

      // Оновлення пагінації
      if (clientSideSearch && filters.search) {
        totalItems.value = processedItems.length;
        totalPages.value = Math.ceil(totalItems.value / pageSize.value) || 1;
      } else {
        // Отримуємо загальну кількість елементів
        const totalCount = paginationData.total || paginationData.totalItems || paginationData.Total || processedItems.length;
        totalItems.value = totalCount;

        // Обчислюємо загальну кількість сторінок
        const itemsPerPage = paginationData.perPage || paginationData.pageSize || pageSize.value;
        const calculatedTotalPages = Math.ceil(totalCount / itemsPerPage);
        totalPages.value = paginationData.totalPages || paginationData.lastPage || paginationData.LastPage || calculatedTotalPages || 1;

        // Встановлюємо поточну сторінку
        currentPage.value = paginationData.page || paginationData.currentPage || paginationData.CurrentPage || page;

        console.log('Pagination updated:', {
          totalItems: totalItems.value,
          totalPages: totalPages.value,
          currentPage: currentPage.value,
          itemsPerPage: itemsPerPage,
          paginationData: paginationData
        });
      }

      console.log('Data fetched successfully:', {
        itemsCount: items.value.length,
        totalItems: totalItems.value,
        totalPages: totalPages.value,
        currentPage: currentPage.value
      });

    } catch (err) {
      // Ігноруємо скасовані запити
      if (err.name === 'CanceledError' || err.code === 'ERR_CANCELED') {
        console.log('🚫 Request was canceled, ignoring error');
        return;
      }

      console.error('Error fetching data:', err);

      // Provide more specific error messages
      let errorMessage = 'Failed to load data';
      if (err.code === 'ECONNREFUSED' || err.message.includes('ECONNREFUSED')) {
        errorMessage = 'Cannot connect to server. Please ensure the backend is running.';
      } else if (err.response?.status === 401) {
        errorMessage = 'Authentication required. Please log in.';
      } else if (err.response?.status === 403) {
        errorMessage = 'Access denied. You do not have permission to view this data.';
      } else if (err.response?.status === 404) {
        errorMessage = 'API endpoint not found.';
      } else if (err.message) {
        errorMessage = err.message;
      }

      error.value = errorMessage;
      items.value = [];
      totalPages.value = 1;
      totalItems.value = 0;
    } finally {
      loading.value = false;
      isLoadingMore.value = false;
      pendingRequests.value.delete(requestKey);

      if (isFirstLoad.value) {
        isFirstLoad.value = false;
      }
    }
  };

  // Функція для застосування оптимістичних оновлень
  const applyOptimisticUpdates = (items) => {
    if (!enableOptimisticUpdates || optimisticUpdates.value.size === 0) {
      return items;
    }

    return items.map(item => {
      const update = optimisticUpdates.value.get(item.id);
      return update ? { ...item, ...update } : item;
    });
  };

  // Функція для додавання оптимістичного оновлення
  const addOptimisticUpdate = (id, updates) => {
    if (!enableOptimisticUpdates) return;

    optimisticUpdates.value.set(id, updates);

    // Автоматично видаляємо через 30 секунд
    setTimeout(() => {
      optimisticUpdates.value.delete(id);
    }, 30000);
  };

  // Функція для видалення оптимістичного оновлення
  const removeOptimisticUpdate = (id) => {
    optimisticUpdates.value.delete(id);
  };

  // Функція для завантаження наступної сторінки (нескінченний скролінг)
  const loadMore = async () => {
    if (!enableInfiniteScroll || isLoadingMore.value || allItemsLoaded.value) {
      return;
    }

    const nextPage = currentPage.value + 1;
    if (nextPage > totalPages.value) {
      allItemsLoaded.value = true;
      return;
    }

    await fetchData(nextPage, { append: true });
  };

  // Функція для примусового оновлення
  const forceRefresh = async () => {
    optimisticUpdates.value.clear();
    allItemsLoaded.value = false;
    await fetchData(1, { force: true });
  };

  // Функція пошуку в елементі (для клієнтського пошуку)
  const searchInItem = (item, searchQuery) => {
    const searchableFields = [
      'name', 'username', 'email', 'title', 'description',
      'contactEmail', 'contactPhone', 'slug',
      // Order-specific fields
      'id', 'customerName', 'customerEmail', 'customerPhone',
      'orderId', 'orderNumber', 'customerId'
    ];

    return searchableFields.some(field => {
      const value = getNestedValue(item, field);
      return value && value.toString().toLowerCase().includes(searchQuery);
    });
  };

  // Допоміжна функція для отримання вкладених значень
  const getNestedValue = (obj, path) => {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  };

  // Скидання фільтрів
  const resetFilters = () => {
    Object.keys(filters).forEach(key => {
      if (key === 'search') {
        filters[key] = '';
      } else {
        filters[key] = defaultFilters[key] || '';
      }
    });
    currentPage.value = 1;
    fetchData(1);
  };

  // Зміна сторінки
  const handlePageChange = (page) => {
    fetchData(page);
  };

  // Пошук з дебаунсом
  let searchTimeout = null;
  const debouncedSearch = () => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    searchTimeout = setTimeout(() => {
      currentPage.value = 1;
      fetchData(1);
    }, debounceTime);
  };

  // Watchers
  watch(() => filters.search, () => {
    console.log('Search changed:', filters.search);
    debouncedSearch();
  });

  // Watcher для інших фільтрів (без дебаунсу)
  Object.keys(defaultFilters).forEach(filterKey => {
    if (filterKey !== 'search') {
      watch(() => filters[filterKey], (newValue, oldValue) => {
        if (newValue !== oldValue) {
          console.log(`Filter ${filterKey} changed:`, filters[filterKey]);
          currentPage.value = 1;
          fetchData(1);
        }
      });
    }
  });

  // Автоматично завантажуємо дані при ініціалізації
  fetchData(1);

  return {
    // Стан
    items,
    loading,
    error,
    isFirstLoad,
    isLoadingMore,
    allItemsLoaded,

    // Пагінація
    currentPage,
    totalPages,
    totalItems,
    pageSize,

    // Фільтри
    filters,
    hasFilters,
    activeFilters,

    // Сортування
    sortBy,
    sortOrder,

    // Методи
    fetchData,
    resetFilters,
    handlePageChange,
    debouncedSearch,
    loadMore,
    forceRefresh,
    addOptimisticUpdate,
    removeOptimisticUpdate
  };
}
